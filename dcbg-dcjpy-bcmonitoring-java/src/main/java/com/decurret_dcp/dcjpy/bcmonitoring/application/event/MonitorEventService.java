package com.decurret_dcp.dcjpy.bcmonitoring.application.event;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDao;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.ContextConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.StructuredLogContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.stereotype.Service;

@Service
public class MonitorEventService {
  private final LoggingService log;
  private final EventLogRepository eventLogRepository;
  private final EventRepository eventRepository;
  private final BlockHeightRepository blockHeightRepository;
  private final ObjectMapper objectMapper;
  private final BcmonitoringConfigurationProperties properties;
  private final AtomicBoolean running = new AtomicBoolean(true);
  private final Web3jConfig web3jConfig;
  private final EthEventLogDao ethEventLogDao;

  public MonitorEventService(
      LoggingService logger,
      EventLogRepository eventLogRepository,
      EventRepository eventRepository,
      BlockHeightRepository blockHeightRepository,
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      EthEventLogDao ethEventLogDao) {
    this.log = logger;
    this.eventLogRepository = eventLogRepository;
    this.eventRepository = eventRepository;
    this.blockHeightRepository = blockHeightRepository;
    this.properties = properties;
    this.web3jConfig = web3jConfig;
    this.objectMapper = new ObjectMapper();
    this.ethEventLogDao = ethEventLogDao;
  }

  /**
   * Execute the monitoring process This method will run in a loop, checking for new events and
   * processing them.
   *
   * @throws NumberFormatException if the check interval is not a valid integer
   * @throw Exception if there is an error during the monitoring process
   */
  public void execute() throws Exception {
    int checkInterval;
    try {
      checkInterval = Integer.parseInt(properties.getSubscription().getCheckInterval());
    } catch (NumberFormatException e) {
      log.error("Failed to convert checkInterval: {}", e.getMessage());
      throw e;
    }
    try {
      monitorEvents();
    } catch (Exception e) {
      log.error("Error in monitoring loop: {}", e.getMessage(), e);
      sleep(checkInterval);
      throw e;
    }
  }

  /**
   * Monitor events from the blockchain and process them. This method will subscribe to new events
   * and process pending transactions.
   *
   * @throw Exception if there is an error during the monitoring process
   */
  private void monitorEvents() throws Exception {
    // Get current block height
    long blockNumber;
    try {
      blockNumber = blockHeightRepository.get();
      log.info("Get blockheight: {}", blockNumber);
    } catch (Exception e) {
      log.error("Failed to get blockheight: {}", e.getMessage());
      throw e;
    }

    try {
      BlockingQueue<Transaction> transactionsQueue = eventLogRepository.subscribe();
      List<Transaction> pendingTransactionsQueue =
          eventLogRepository.getFilterLogs(blockNumber + 1);

      processPendingTransactions(pendingTransactionsQueue);
      processNewTransactions(transactionsQueue);
    } catch (Exception e) {
      log.error("Error in monitoring: {}", e.getMessage());
      ethEventLogDao.unsubscribe();
      throw e;
    }
  }

  /**
   * Process pending transactions from the queue. This method will save the block height and events
   * to the database.
   *
   * @param pendingQueue BlockingQueue of pending transactions
   */
  private void processPendingTransactions(List<Transaction> pendingQueue) throws Exception {
    BlockHeight exBlockHeight = BlockHeight.builder().blockNumber(0).build();

    for (Transaction tx : pendingQueue) {
      try {
        // Process block height change
        if (exBlockHeight.blockNumber != 0
            && exBlockHeight.blockNumber != tx.blockHeight.blockNumber) {
          if (!savePendingTransactionBlockNumber(exBlockHeight)) {
            throw new Exception("Failed to save block height");
          }
        }

        if (tx.blockHeight.blockNumber == 0) {
          throw new RuntimeException("Block height Number is zero");
        } else if (!savePendingTransaction(tx)) {
          throw new Exception("Failed to save transaction");
        }

        exBlockHeight = tx.blockHeight;
      } catch (Exception e) {
        log.error("Error while processing pending transactions: {}", e.getMessage());
        throw e;
      }
    }
    log.info("Success to process pending transactions");
  }

  /**
   * Process new transactions from the queue. This method will save the events to the database.
   *
   * @param transactionsQueue BlockingQueue of new transactions
   */
  private void processNewTransactions(BlockingQueue<Transaction> transactionsQueue)
      throws Exception {
    while (ContextConfig.isServiceRunning()) {
      try {
        Transaction tx = transactionsQueue.poll(5, TimeUnit.SECONDS);
        if (Objects.isNull(tx)) continue;
        if (tx.blockHeight.blockNumber == -1) {
          throw new Exception("Websocket is disconnected");
        }
        if (tx.blockHeight.blockNumber == 0) {
          throw new Exception("Block height Number is zero");
        }

        if (!saveTransaction(tx)) {
          throw new Exception("Failed to save transaction");
        }

      } catch (Exception e) {
        log.error("Error while processing new transactions: ", e.getMessage());
        throw e;
      }
      log.info("Success to process new transactions");
    }
  }

  /**
   * Save transaction to the database.
   *
   * @param tx Transaction object containing the events and block height
   * @return true if the transaction was saved successfully, false otherwise
   */
  private boolean saveTransaction(Transaction tx) {
    // Process all events in the transaction
    for (Event e : tx.events) {
      if (e.transactionHash.isEmpty()) {
        log.error("Event transaction hash is zero");
        return false;
      }

      String traceId = fetchTraceId(e.nonIndexedValues);
      try (var logContext =
          StructuredLogContext.forBlockchainEvent(
              e.name,
              e.transactionHash,
              tx.blockHeight.blockNumber,
              e.logIndex,
              e.blockTimestamp,
              traceId)) {

        if (!eventRepository.save(e)) {
          log.error("Failure to register event");
          return false;
        }
        log.info("Success to register event");
      }
    }

    if (!blockHeightRepository.save(tx.blockHeight)) {
      log.error("Failure to register block number");
      return false;
    }
    log.info("Success to register block number");
    return true;
  }

  /**
   * Save pending transaction to the database.
   *
   * @param tx Transaction object containing the events and block height
   * @return true if the transaction was saved successfully, false otherwise
   */
  private boolean savePendingTransaction(Transaction tx) {
    for (Event e : tx.events) {
      if (e.transactionHash.isEmpty()) {
        log.error("Event transaction hash is zero");
        return false;
      }

      String traceId = fetchTraceId(e.nonIndexedValues);
      try (var logContext =
          StructuredLogContext.forBlockchainEvent(
              e.name,
              e.transactionHash,
              tx.blockHeight.blockNumber,
              e.logIndex,
              e.blockTimestamp,
              traceId)) {

        if (!eventRepository.save(e)) {
          log.error("Failure to register event");
          return false;
        }
        log.info("Success to register event");
      }
    }
    return true;
  }

  /**
   * Save pending transaction block number to the database.
   *
   * @param blockHeight BlockHeight object containing the block number
   * @return true if the block height was saved successfully, false otherwise
   */
  private boolean savePendingTransactionBlockNumber(BlockHeight blockHeight) {
    if (!blockHeightRepository.save(blockHeight)) {
      log.error("Failure to register block number: {}", blockHeight.blockNumber);
      return false;
    }
    log.info("Success to register block number: {}", blockHeight.blockNumber);
    return true;
  }

  private static class ParsedTraceId {
    public byte[] traceId;
  }

  /**
   * Fetch trace ID from non-indexed values.
   *
   * @param nonIndexedValues Non-indexed values as a JSON string
   * @return Trace ID as a string
   */
  private String fetchTraceId(String nonIndexedValues) {
    try {
      ParsedTraceId parsed = objectMapper.readValue(nonIndexedValues, ParsedTraceId.class);
      if (parsed.traceId == null || parsed.traceId.length == 0) {
        return "";
      }

      StringBuilder sb = new StringBuilder();
      for (byte b : parsed.traceId) {
        if (b != 0) {
          sb.append((char) b);
        }
      }
      return sb.toString();
    } catch (JsonProcessingException e) {
      log.error("Error parsing trace ID: {}", e.getMessage());
      return "";
    }
  }

  /**
   * Sleep for a specified number of milliseconds.
   *
   * @param milliseconds Number of milliseconds to sleep
   */
  private void sleep(int milliseconds) {
    try {
      Thread.sleep(milliseconds);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
  }
}
