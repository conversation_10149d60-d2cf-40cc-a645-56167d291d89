package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.reactivex.disposables.Disposable;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventValues;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterNumber;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.tx.Contract;

@Component
public class EthEventLogDao {
  private final LoggingService logger;
  private final BcmonitoringConfigurationProperties properties;
  private final Web3jConfig web3jConfig;
  private final AbiParser abiParser;
  private final ObjectMapper objectMapper;
  private Disposable subscription;

  /**
   * Constructor for EthEventLogDao.
   *
   * @param log The logging service.
   * @param properties The configuration properties.
   * @param web3jConfig The Web3j configuration.
   * @param abiParser The ABI parser.
   * @param objectMapper The object mapper.
   */
  public EthEventLogDao(
      LoggingService log,
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      AbiParser abiParser,
      ObjectMapper objectMapper) {
    this.logger = log;
    this.properties = properties;
    this.web3jConfig = web3jConfig;
    this.abiParser = abiParser;
    this.objectMapper = objectMapper;
  }

  /**
   * Subscribes to all blocks and processes transactions.
   *
   * @return A BlockingQueue of Transaction objects.
   */
  public BlockingQueue<Transaction> subscribeAll() {
    BlockingQueue<Transaction> transactions = new LinkedBlockingQueue<>(Integer.MAX_VALUE);

    // Check if the difference is valid
    int allowableDiff;
    try {
      allowableDiff =
          Integer.parseInt(properties.getSubscription().getAllowableBlockTimestampDiffSec());
    } catch (NumberFormatException e) {
      logger.error("Failed to parse allowable timestamp difference", e);
      return null;
    }

    try {
      // Create a new Web3j instance for this subscription
      Web3j web3j = web3jConfig.getWeb3j();
      // Subscribe to new blocks
      this.subscription =
          web3j
              .newHeadsNotifications()
              .subscribe(
                  newHeadsNotification -> {
                    try {
                      web3j
                          .ethGetBlockByNumber(
                              () -> newHeadsNotification.getParams().getResult().getNumber(), true)
                          .sendAsync()
                          .thenApply(EthBlock::getBlock)
                          .thenAccept(
                              block -> {
                                BigInteger blockNumber = block.getNumber();

                                // Check delay in block processing
                                if (isDelayed(block, allowableDiff)) {
                                  // todo: update log message to similar to go
                                  logger.warn(
                                      "Block {} is delayed by more than {} seconds",
                                      blockNumber,
                                      allowableDiff);
                                }

                                // Process block transactions and events
                                List<Event> events = convBlock2EventEntities(block);
                                if (!events.isEmpty()) {

                                  BlockHeight blockHeight =
                                      BlockHeight.builder()
                                          .blockNumber(blockNumber.longValue())
                                          .build();
                                  Transaction transaction =
                                      Transaction.builder()
                                          .events(events)
                                          .blockHeight(blockHeight)
                                          .build();

                                  try {
                                    transactions.put(transaction);
                                  } catch (InterruptedException e) {
                                    throw new RuntimeException(e);
                                  }
                                }
                              });
                    } catch (Exception e) {
                      logger.error("Error processing block", e);
                    }
                  },
                  error -> {
                    logger.error("Subscription error", error);
                    unsubscribe();
                    web3jConfig.shutdownWeb3j();
                    transactions.put(
                        Transaction.builder()
                            .blockHeight(BlockHeight.builder().blockNumber(-1).build())
                            .build());
                  },
                  () -> logger.info("Subscription completed"));
      return transactions;
    } catch (Exception e) {
      logger.error("Failed to create Web3j subscription", e);
      throw e;
    }
  }

  /**
   * Checks if the block is delayed based on the allowable difference in seconds.
   *
   * @param block The block to check.
   * @param allowableDiffSeconds The allowable difference in seconds.
   * @return true if the block is delayed, false otherwise.
   */
  private boolean isDelayed(EthBlock.Block block, int allowableDiffSeconds) {
    long blockTimestamp = block.getTimestamp().longValue();
    long currentTime = Instant.now().getEpochSecond();
    long diff = currentTime - blockTimestamp;

    return diff > allowableDiffSeconds;
  }

  /**
   * Converts a block to a collection of event entities.
   *
   * @param block Ethereum block
   * @return Collection of events found in the block
   * @throws IOException If there is an error communicating with the Ethereum node
   * @throws ExecutionException If there is an error executing the transaction
   * @throws InterruptedException If the operation is interrupted
   */
  public List<Event> convBlock2EventEntities(EthBlock.Block block) {
    List<Event> events = new ArrayList<>();

    try {
      // Create a new Web3j instance for this operation
      Web3j web3j = web3jConfig.getWeb3j();

      for (EthBlock.TransactionResult txResult : block.getTransactions()) {
        try {
          EthGetTransactionReceipt receiptResponse =
              web3j.ethGetTransactionReceipt(txResult.get().toString()).send();

          TransactionReceipt receipt = receiptResponse.getTransactionReceipt().orElse(null);
          if (receipt == null) {
            continue;
          }

          for (Log log : receipt.getLogs()) {
            try {
              logger.info("Event found tx_hash={}", log.getTransactionHash());
              Event event =
                  convertEthLogToEventEntity(log)
                      .withBlockTimestamp(block.getTimestamp().longValue());
              logger.info("Event parsed tx_hash={}, name={}", event.transactionHash, event.name);

              if (event.transactionHash != null && !event.transactionHash.isEmpty()) {
                events.add(event);
              }
            } catch (Exception e) {
              logger.error("Error processing log for transaction {}", log.getTransactionHash());
            }
          }
        } catch (Exception e) {
          logger.error("Error processing transaction", e);
        }
      }
    } catch (Exception e) {
      logger.error("Error creating Web3j instance", e);
    }

    return events;
  }

  /**
   * Converts an Ethereum log to an Event entity.
   *
   * @param ethLog The Ethereum log to convert
   * @return Converted Event entity
   * @throws Exception If conversion fails
   */
  public Event convertEthLogToEventEntity(Log ethLog) throws Exception {
    try {
      // Get ABI event definition for the log
      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);
      // todo: never return null
      if (abiEvent == null) {
        logger.info("Event definition not found in ABI");
        throw new Exception("Event definition not found in ABI");
      }

      // Extract event parameters using Web3j's utilities
      EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);
      if (eventValues == null) {
        logger.info("No event values found for log: {}", ethLog);
        throw new Exception("No event values found for log");
      }

      // Process indexed parameters
      Map<String, Object> indexedValues = new HashMap<>();
      List<Type> indexedParameters = eventValues.getIndexedValues();
      List<TypeReference<Type>> indexedReferences = abiEvent.getIndexedParameters();

      for (int i = 0; i < indexedParameters.size(); i++) {
        // wrong name
        String name = indexedReferences.get(i).getType().getTypeName();
        Object value = indexedParameters.get(i).getValue();
        indexedValues.put(name, value);
      }
      String indexedJson = objectMapper.writeValueAsString(indexedValues);

      // Process non-indexed parameters
      Map<String, Object> nonIndexedValues = new HashMap<>();
      List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();
      List<TypeReference<Type>> nonIndexedReferences = abiEvent.getNonIndexedParameters();

      for (int i = 0; i < nonIndexedParameters.size(); i++) {
        // wrong name
        String name = nonIndexedReferences.get(i).getType().getTypeName();
        Object value = nonIndexedParameters.get(i).getValue();
        nonIndexedValues.put(name, value);
      }
      String nonIndexedJson = objectMapper.writeValueAsString(nonIndexedValues);

      // Serialize log to JSON
      String logJson = objectMapper.writeValueAsString(ethLog);

      // Create and return new Event entity
      return Event.builder()
          .name(abiEvent.getName())
          .transactionHash(ethLog.getTransactionHash())
          .logIndex((int) ethLog.getLogIndex().longValue())
          .indexedValues(indexedJson)
          .nonIndexedValues(nonIndexedJson)
          .log(logJson)
          .build();
    } catch (Exception e) {
      logger.error("Error converting log to event entity", e);
      return null;
    }
  }

  /**
   * Retrieves the block timestamp for a given block number.
   *
   * @param blockNumber The block number to retrieve the timestamp for.
   * @return The block timestamp in seconds since epoch.
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private long getBlockTimestamp(BigInteger blockNumber) throws IOException {
    // Create a new Web3j instance for this operation
    Web3j web3j = web3jConfig.getWeb3j();

    try {
      return web3j
          .ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false)
          .send()
          .getBlock()
          .getTimestamp()
          .longValue();
    } finally {
      // Shutdown the Web3j instance to free resources
      web3j.shutdown();
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight) {
    return getPendingTransactions(blockHeight, false);
  }

  /**
   * Get filtered logs from a specific block height with an option to force an error in the outer
   * catch block This method is primarily used for testing the error handling in the outer catch
   * block
   *
   * @param blockHeight Block number to start from
   * @param forceOuterError Whether to force an error in the outer catch block (for testing)
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight, boolean forceOuterError) {
    try {
      // Create a new Web3j instance for this operation
      Web3j web3j = web3jConfig.getWeb3j();

      // Create filter to get logs from the specified block height
      EthFilter filter =
          new EthFilter(
              DefaultBlockParameter.valueOf(BigInteger.valueOf(blockHeight)),
              DefaultBlockParameter.valueOf("latest"),
              Collections.emptyList());

      // Get logs synchronously
      List<EthLog.LogResult> filterLogs = web3j.ethGetLogs(filter).send().getLogs();

      logger.info(
          "Retrieved {} logs from block height {} to latest", filterLogs.size(), blockHeight);

      // Collect block numbers
      List<BigInteger> blockNumbers =
          filterLogs.stream().map(result -> (Log) result.get()).map(Log::getBlockNumber).toList();

      // Fetch timestamps per block
      Map<BigInteger, BigInteger> blockTimestamps = new HashMap<>();
      for (BigInteger blockNumber : blockNumbers) {
        EthBlock block =
            web3j.ethGetBlockByNumber(new DefaultBlockParameterNumber(blockNumber), false).send();
        blockTimestamps.put(blockNumber, block.getBlock().getTimestamp());
      }

      if (forceOuterError) {
        throw new RuntimeException("Forced error in outer catch block for testing");
      }

      return filterLogs.stream()
          .map(
              logResult -> {
                try {
                  Log ethLog = (Log) logResult.get();
                  logger.info("Event found tx_hash={}", ethLog.getTransactionHash());

                  Event event =
                      convertEthLogToEventEntity(ethLog)
                          .withBlockTimestamp(
                              blockTimestamps.get(ethLog.getBlockNumber()).longValue());
                  logger.info(
                      "Event parsed tx_hash={}, name={}", event.transactionHash, event.name);

                  BlockHeight height =
                      BlockHeight.builder()
                          .blockNumber(ethLog.getBlockNumber().longValue())
                          .build();

                  return Transaction.builder()
                      .events(Collections.singletonList(event))
                      .blockHeight(height)
                      .build();
                } catch (Exception e) {
                  logger.error("Error processing individual log", e);
                  return null;
                }
              })
          .filter(Objects::nonNull)
          .toList();

    } catch (Exception e) {
      logger.error("Error getting filtered logs", e);
      throw new RuntimeException("Error getting filtered logs", e);
    }
  }

  public void unsubscribe() {
    if (subscription != null) {
      subscription.dispose();
    }
  }
}
