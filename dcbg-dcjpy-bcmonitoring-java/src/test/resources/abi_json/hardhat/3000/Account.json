{"address": "0x993366A606A99129e56B4b99B27e428ba1Cb672f", "abi": [{"type": "event", "anonymous": false, "name": "Account<PERSON><PERSON>bled", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "bytes32", "name": "accountStatus", "indexed": false}, {"type": "bytes32", "name": "reasonCode", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AccountTerminated", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "reasonCode", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddAccountRole", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "address", "name": "accountEoa", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddZone", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ForceBurn", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}, {"type": "uint256", "name": "burnedAmount", "indexed": false}, {"type": "uint256", "name": "burnedBalance", "indexed": false}, {"type": "tuple[]", "name": "forceDischarge", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "uint256", "name": "dischargeAmount"}]}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "addAccount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "validatorId"}], "outputs": []}, {"type": "function", "name": "addAccountRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "address", "name": "accountEoa"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addZone", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "approve", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "balanceOf", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "uint256", "name": "balance"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "burn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "calcAllowance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "calcBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "fromA<PERSON>unt"}, {"type": "bytes32", "name": "toAccount"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "uint256", "name": "fromAccountBalance"}, {"type": "uint256", "name": "toAccountBalance"}]}, {"type": "function", "name": "editBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "calcPattern"}], "outputs": [{"type": "uint256", "name": "balance"}]}, {"type": "function", "name": "forceBurn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "getAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountData", "components": [{"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountDataAll", "components": [{"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}, {"type": "tuple[]", "name": "businessZoneAccounts", "components": [{"type": "string", "name": "accountName"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountLimit", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountLimitData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountsAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "account", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}]}, {"type": "function", "name": "getAllowance", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "bytes32", "name": "spenderId"}], "outputs": [{"type": "uint256", "name": "allowance"}, {"type": "uint256", "name": "approvedAt"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAllowanceList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "ownerId"}, {"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}], "outputs": [{"type": "tuple[]", "name": "approvalData", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getDestinationAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "string", "name": "accountName"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getValidatorIdByAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getZoneByAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple[]", "name": "zones", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}]}, {"type": "function", "name": "hasAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "isActivated", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "isFrozen", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "frozen"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "isTerminated", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "terminated"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "mint", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "modAccount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setAccountAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "account", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setAccountStatus", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setTerminated", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x7f70d18c96443201382fe5153031f6a3ee9037d78ff0c806d14751b6159c5c54", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0x993366A606A99129e56B4b99B27e428ba1Cb672f", "transactionIndex": 0, "gasUsed": "5994001", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xa9748d716a432c0cab7cc342ef262c27a200d9aeaa73a0708cdf8c0cb59acc4c", "blockNumber": 330, "cumulativeGasUsed": "5994001", "status": 1}}