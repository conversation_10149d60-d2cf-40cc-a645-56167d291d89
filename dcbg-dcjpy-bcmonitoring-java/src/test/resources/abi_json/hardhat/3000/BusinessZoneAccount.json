{"address": "0xA2bEBB60A988db6dE39EFa231fDB5C5288A8b6Cd", "abi": [{"type": "event", "anonymous": false, "name": "BalanceUpdateByIssueVoucher", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "BalanceUpdateByRedeemVoucher", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetActiveBusinessAccountWithZone", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncBusinessZoneBalance", "inputs": [{"type": "tuple", "name": "transferData", "indexed": false, "components": [{"type": "bytes32", "name": "transferType"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "fromValidatorId"}, {"type": "bytes32", "name": "toValidatorId"}, {"type": "uint256", "name": "fromAccountBalance"}, {"type": "uint256", "name": "toAccountBalance"}, {"type": "uint256", "name": "businessZoneBalance"}, {"type": "uint16", "name": "bizZoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}]}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncBusinessZoneStatus", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bytes32", "name": "accountStatus", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "accountIdExistenceByZoneId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}]}, {"type": "function", "name": "addBusinessZoneBalance", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "balanceUpdateByIssueVoucher", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "balanceUpdateByRedeemVoucher", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "forceBurnAllBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "uint256", "name": "burnedAmount"}, {"type": "tuple[]", "name": "forceDischarge", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "uint256", "name": "dischargeAmount"}]}]}, {"type": "function", "name": "getBizAccountsAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "bizAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}]}, {"type": "function", "name": "getBusinessZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}]}, {"type": "function", "name": "hasAccountByZone", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "isActivatedByZone", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "setActiveBusinessAccountWithZone", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setBizAccountsAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "bizAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setBizZoneTerminated", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}], "outputs": []}, {"type": "function", "name": "subtractBusinessZoneBalance", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": []}, {"type": "function", "name": "syncBusinessZoneBalance", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "params", "components": [{"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}]}], "outputs": []}, {"type": "function", "name": "syncBusinessZoneStatus", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x77634ad10372f89cc38a8c45a2fce070f73bc11e44738de3dc9e3bd431580cd6", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0xA2bEBB60A988db6dE39EFa231fDB5C5288A8b6Cd", "transactionIndex": 0, "gasUsed": "3865327", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x34079f30e093e33a8a67c6f6dac35f9d2caf58598361d245ec88389ebd7ca8de", "blockNumber": 339, "cumulativeGasUsed": "3865327", "status": 1}}